<script lang="ts">
	// import * as Form from '$lib/components/ui/form';
	// import { Input } from '$lib/components/ui/input';
	// import * as Alert from '$lib/components/ui/alert';

	import { formSchema, type FormSchema, type Response } from './schema';
	import SuperDebug, { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { Button, Label, Input, Alert, Spinner } from 'flowbite-svelte';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import { Field, Control, FieldErrors } from 'formsnap';
	import {
		EyeOutline,
		EyeSlashOutline,
		UserOutline,
		LockOutline
	} from 'flowbite-svelte-icons';
	import { writable, derived, get } from 'svelte/store';
	import { onMount, onDestroy } from 'svelte';
	import type { LanguageCode } from '$src/lib/stores/user';
	import en from '$lib/locales/en.json';
	import th from '$lib/locales/th.json';

	export let data: SuperValidated<Infer<FormSchema>>;
	export let response: Response;

	// Local language store for this component only (defaults to Thai)
	const localLanguage = writable<LanguageCode>('th');

	// Local dictionaries
	const dictionaries = { en, th } as const;

	// Local translation function that uses the local language state
	const localDict = derived(localLanguage, ($localLang) => {
		return dictionaries[$localLang] ?? dictionaries.en;
	});

	// Local translation function - reactive
	$: t = (key: string): string => {
		return ($localDict && ($localDict as Record<string, string>)[key]) ?? key;
	};

	// Language toggle function
	function toggleLanguage() {
		localLanguage.update((lang) => (lang === 'en' ? 'th' : 'en'));
	}

	const form = superForm(data, {
		validators: zodClient(formSchema)
	});

	const { form: formData, enhance, delayed } = form;

	// Client-side validation state for username
	let usernameValidationError = false;
	let usernameValidationErrorShow = false;

	// Client-side validation state for password
	let passwordValidationError = false;

	// Username validation function
	function validateUsername(username: string) {
		if (!username.trim()) return { isValid: true, showError: false }; // Empty username is handled by required field
		
		// Check minimum length
		if (username.length < 3) {
			return { isValid: false, showError: false };
		}

		// Check maximum length
		if (username.length > 20) {
			return { isValid: false, showError: false };
		}
		
		// Check if starts with dot, underscore, or hyphen
		if (/^[._-]/.test(username)) {
			return { isValid: false, showError: true };
		}
		
		// Check for multiple consecutive hyphens
		if (/--/.test(username)) {
			return { isValid: false, showError: false };
		}
		
		// Check for multiple consecutive dots
		if (/\.\./.test(username)) {
			return { isValid: false, showError: false };
		}
		
		// Check for multiple consecutive underscores
		if (/__/.test(username)) {
			return { isValid: false, showError: false };
		}
		
		// Check if only contains allowed characters
		if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
			return { isValid: false, showError: true };
		}
		
		// Check for blocked words (including standalone words and between hyphens or dots)
		const blockedWords = [
			'union', 'select', 'from', 'where', 'null', 'and', 'or', 'waitfor', 'delay', 'sleep',
			'drop', 'exec', 'call', 'insert', 'update', 'delete', 'create', 'alter', 'table',
			'into', 'set', 'echo', 'passwd', 'whoami', 'system32', 'windows'
		];
		
		// Check if entire username is a blocked word
		const lowerUsername = username.toLowerCase();
		if (blockedWords.includes(lowerUsername)) {
			return { isValid: false, showError: false };
		}
		
		// Also check segments split by hyphens and dots
		const segments = lowerUsername.split(/[-.]/).filter(segment => segment.length > 0);
		
		for (const segment of segments) {
			if (blockedWords.includes(segment)) {
				return { isValid: false, showError: false };
			}
		}
		
		return { isValid: true, showError: false };
	}

	// Password visibility toggle state
	let showPassword = false;

	// Hide login error when user starts typing
	let hideLoginError = false;
	
	// Reset hideLoginError when response changes (new login attempt)
	$: if (response?.form?.message?.status === 'fail') {
		hideLoginError = false;
		console.log(response);
	}

	// Username input handler with filtering and validation
	function handleUsernameInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// Filter out invalid characters - allow letters, numbers, dots, underscores, and hyphens
		let filteredValue = target.value.replace(/[^a-zA-Z0-9._-]/g, '');

		// Remove SQL comment patterns (consecutive hyphens)
		filteredValue = filteredValue.replace(/--+/g, '-');
		
		// Remove consecutive dots
		filteredValue = filteredValue.replace(/\.\.+/g, '.');
		
		// Remove consecutive underscores
		filteredValue = filteredValue.replace(/__+/g, '_');

		// Enforce 20-character limit
		if (filteredValue.length > 20) {
			filteredValue = filteredValue.substring(0, 20);
		}

		// Update form data with filtered value
		$formData.username = filteredValue;

		// Validate username with comprehensive rules
		const validation = validateUsername(filteredValue);
		usernameValidationError = !validation.isValid;
		usernameValidationErrorShow = validation.showError;

		// Clear login error when user types
		if (filteredValue.length > 0) {
			hideLoginError = true;
		}

		// Update the input value to reflect the filtered value (only for manual input, not autofill)
		if (event.type === 'input' && event.isTrusted) {
			target.value = filteredValue;
		}
	}

	// Handle password input event
	function handlePasswordInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// Update form data to ensure sync
		$formData.password = target.value;

		// Clear validation error when user types
		if (target.value.trim() !== '') {
			passwordValidationError = false;
		}

		// Clear login error when user types
		if (target.value.length > 0) {
			hideLoginError = true;
		}
	}

	// Handle paste events to filter pasted content
	function handleUsernamePaste(event: ClipboardEvent) {
		event.preventDefault();

		const pastedText = event.clipboardData?.getData('text') || '';

		// Filter pasted content - allow letters, numbers, dots, underscores, and hyphens
		let filteredText = pastedText.replace(/[^a-zA-Z0-9._-]/g, '');

		// Remove SQL comment patterns (consecutive hyphens)
		filteredText = filteredText.replace(/--+/g, '-');
		
		// Remove consecutive dots
		filteredText = filteredText.replace(/\.\.+/g, '.');
		
		// Remove consecutive underscores
		filteredText = filteredText.replace(/__+/g, '_');

		// Enforce 20-character limit
		if (filteredText.length > 20) {
			filteredText = filteredText.substring(0, 20);
		}

		// Update form data with filtered value
		$formData.username = filteredText;

		// Validate username with comprehensive rules
		const validation = validateUsername(filteredText);
		usernameValidationError = !validation.isValid;
		usernameValidationErrorShow = validation.showError;

		// Update the input element directly
		const target = event.target as HTMLInputElement;
		target.value = filteredText;

		// Trigger input event to ensure reactivity
		target.dispatchEvent(new Event('input', { bubbles: true }));
	}

	// Toggle password visibility
	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}

	// Reactive statement for form validation state
	$: isUsernameValid = $formData.username.trim() !== '' && !usernameValidationError;
	$: isPasswordValid = $formData.password.trim() !== '' && !passwordValidationError;
	$: isFormValid = isUsernameValid && isPasswordValid;

	// Autofill detection variables
	let usernameElement: HTMLInputElement;
	let passwordElement: HTMLInputElement;
	let autofillCheckInterval: ReturnType<typeof setInterval>;
	let lastUsernameValue = '';
	let lastPasswordValue = '';
	let mutationObserver: MutationObserver;

	// Function to check for autofill changes
	function checkAutofillChanges() {
		if (usernameElement) {
			const currentUsername = usernameElement.value;
			if (currentUsername !== lastUsernameValue) {
				lastUsernameValue = currentUsername;
				// Sync with form data and trigger validation
				$formData.username = currentUsername;
				// Create synthetic event to trigger validation
				const syntheticEvent = new Event('input', { bubbles: true });
				Object.defineProperty(syntheticEvent, 'target', {
					value: usernameElement,
					enumerable: true
				});
				handleUsernameInput(syntheticEvent);
			}
		}

		if (passwordElement) {
			const currentPassword = passwordElement.value;
			if (currentPassword !== lastPasswordValue) {
				lastPasswordValue = currentPassword;
				// Sync with form data and trigger validation
				$formData.password = currentPassword;
				// Create synthetic event to trigger validation
				const syntheticEvent = new Event('input', { bubbles: true });
				Object.defineProperty(syntheticEvent, 'target', {
					value: passwordElement,
					enumerable: true
				});
				handlePasswordInput(syntheticEvent);
			}
		}
	}

	// MutationObserver to detect DOM changes from autofill
	function setupMutationObserver() {
		if (typeof MutationObserver !== 'undefined' && (usernameElement || passwordElement)) {
			mutationObserver = new MutationObserver((mutations) => {
				mutations.forEach((mutation) => {
					if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
						checkAutofillChanges();
					}
				});
			});

			// Observe both input elements
			if (usernameElement) {
				mutationObserver.observe(usernameElement, {
					attributes: true,
					attributeFilter: ['value']
				});
			}
			if (passwordElement) {
				mutationObserver.observe(passwordElement, {
					attributes: true,
					attributeFilter: ['value']
				});
			}
		}
	}

	// Setup autofill detection on mount
	onMount(() => {
		// Get references to input elements
		usernameElement = document.getElementById('username') as HTMLInputElement;
		passwordElement = document.getElementById('password') as HTMLInputElement;

		// Initialize last values
		lastUsernameValue = usernameElement?.value || '';
		lastPasswordValue = passwordElement?.value || '';

		// Setup polling for autofill detection
		autofillCheckInterval = setInterval(checkAutofillChanges, 100);

		// Setup MutationObserver
		setupMutationObserver();

		// Also check immediately after a short delay to catch early autofill
		setTimeout(checkAutofillChanges, 500);
	});

	// Cleanup on destroy
	onDestroy(() => {
		if (autofillCheckInterval) {
			clearInterval(autofillCheckInterval);
		}
		if (mutationObserver) {
			mutationObserver.disconnect();
		}
	});
</script>

<!-- <SuperDebug data={response} /> -->
<!-- Main Container -->
<!-- <br /> -->

<div class="row text-center">
	<!-- Left Side - Image -->
	<!-- <div class="column">
        <img src="/images/Salmate-Logo-Transparent.png" alt="Salmate Logo" class="w-64 h-64 object-contain">
    </div> -->

	<!-- Right Side - Login -->
	<div class="column">
		<form class="flex flex-col" action="?/login" method="POST" use:enhance>
			<h3 class="dark:text-white mb-10 text-center text-5xl font-bold text-gray-900">Salmate</h3>
			<!-- Language Toggle Button -->
			<div class="mb-2 flex justify-center">
				<button
					type="button"
					on:click={toggleLanguage}
					class="flex items-center gap-2 rounded-lg bg-white px-3 py-2 text-xs font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
					aria-label="Toggle language"
				>
					{#if $localLanguage === 'en'}
						<span>🇺🇸</span>
						<span>{t('language_name_en')}</span>
					{:else}
						<span>🇹🇭</span>
						<span>{t('language_name_th')}</span>
					{/if}
				</button>
			</div>

			<div class="mb-4">
				<Field {form} name="username">
					<Control let:attrs>
						<Label class="mb-1 text-left" for="username">{t('login_username_label')}</Label>
						<div class="relative">
							<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
								<UserOutline class="h-5 w-5 text-gray-500" />
							</div>
							<Input
								{...attrs}
								id="username"
								name="username"
								type="text"
								bind:value={$formData.username}
								placeholder={t('login_username_placeholder')}
								class="focus:ring-blue ps-10 focus:border-transparent focus:ring-2 {usernameValidationError
									? 'border-2 border-red-500 focus:ring-red-500'
									: ''}"
								autocomplete="username"
								on:input={handleUsernameInput}
								on:change={handleUsernameInput}
								on:blur={handleUsernameInput}
								on:paste={handleUsernamePaste}
								minlength={3}
								autofocus
							/>
						</div>
						{#if usernameValidationErrorShow}
							<Alert id="username-error" color="red" class="mt-1 px-3 py-1 text-left text-xs font-medium text-red-500">
								{t('login_username_validation_error')}
							</Alert>
							<!-- Redundant -->
							<!-- {:else}
							<FieldErrors /> -->
						{/if}
					</Control>
				</Field>
			</div>
			<!-- <br /> -->
			<div class="mb-4">
				<Field {form} name="password">
					<Control let:attrs>
						<Label class="mb-1 text-left" for="password">{t('login_password_label')}</Label>
						<div class="relative">
							<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
								<LockOutline class="h-5 w-5 text-gray-500" />
							</div>
							<Input
								{...attrs}
								id="password"
								name="password"
								type={showPassword ? 'text' : 'password'}
								bind:value={$formData.password}
								placeholder={t('login_password_placeholder')}
								class="focus:ring-blue pe-10 ps-10 focus:border-transparent focus:ring-2 {passwordValidationError
									? 'border-red-500 focus:ring-red-500'
									: ''}"
								autocomplete="current-password"
								on:input={handlePasswordInput}
								on:change={handlePasswordInput}
								on:blur={handlePasswordInput}
								maxlength={50}
							/>
							<button
								type="button"
								class="absolute inset-y-0 end-0 flex cursor-pointer items-center pe-3.5 transition-colors hover:text-blue-500"
								on:click={togglePasswordVisibility}
								tabindex="-1"
								aria-label={showPassword ? 'Hide password' : 'Show password'}
							>
								{#if showPassword}
									<EyeSlashOutline class="h-5 w-5 text-gray-500 hover:text-blue-500" />
								{:else}
									<EyeOutline class="h-5 w-5 text-gray-500 hover:text-blue-500" />
								{/if}
							</button>
						</div>
						<!-- No validation for password -->
						<!-- {#if passwordValidationError}
							<Alert color="red" class="mt-1 px-3 py-1 text-left text-xs">
								{passwordValidationError}
							</Alert>
						{:else}
							<FieldErrors />
						{/if} -->
					</Control>
				</Field>
			</div>
			<!-- <br /> -->
			<div class="flex flex-col gap-0">
				<Button
					type="submit"
					class="w-full bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600"
				>
					{#if $delayed}
						<Spinner class="me-3" size="4" color="white" data-testid="loading-spinner" /> {t('login_submit_loading')}
					{:else}
						{t('login_submit_button')}
					{/if}
				</Button>
				{#if response?.form?.message?.status == 'fail' && !hideLoginError}
					{#if response?.form?.message?.detail == "Invalid username or password"}
						<Alert
							id="login-error"
							color="red"
							class="mt-1 flex justify-center gap-1 px-3 py-1 text-xs font-medium text-red-500"
						>
							<!-- Use local error message instead instead server message -->
							<!-- {response?.form?.message?.detail || t('login_submit_error')} -->
							{t('login_submit_error_p1')} <br /> {t('login_submit_error_p2')}
						</Alert>
					{:else if response?.form?.message?.detail == "This account has been deactivated."}
						<Alert
							id="login-error"
							color="red"
							class="mt-1 flex justify-center gap-1 px-3 py-1 text-xs font-medium text-red-500"
						>
							{t('login_user_deactivated_p1')} <br /> {t('login_user_deactivated_p2')}
						</Alert>
					{/if}
				{/if}
			</div>
		</form>
	</div>
</div>
